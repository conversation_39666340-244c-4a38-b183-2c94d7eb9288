{"name": "parking-client-ui", "version": "1.6.18", "main": "electron/main.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --port 4173", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "electron": "wait-on tcp:3000 && electron .", "electron:dev": "concurrently -k \"npm run dev\" \"npm run electron\"", "electron:build": "vite build && electron-builder", "electron:publish": "node ./src/package.js && vite build && electron-builder"}, "dependencies": {"@element-plus/icons-vue": "^2.0.9", "axios": "^0.25.0", "big.js": "^6.2.2", "electron-log": "^4.4.8", "electron-updater": "^5.3.0", "element-plus": "^2.2.26", "lodash": "^4.17.21", "mitt": "^3.0.0", "mockjs": "^1.1.0", "node-rtsp-stream": "^0.0.9", "nprogress": "^0.2.0", "pinia": "^2.0.17", "pinia-plugin-persistedstate": "^2.1.1", "uuid": "^9.0.0", "vue": "^3.2.37", "vue-router": "^4.1.3", "vue3-video-play": "^1.3.1-beta.6"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.4", "@vitejs/plugin-vue": "^3.0.1", "@vue/eslint-config-prettier": "^7.0.0", "@vueuse/electron": "^9.5.0", "concurrently": "^7.3.0", "cross-env": "^7.0.3", "electron": "^20.3.12", "electron-builder": "^23.6.0", "eslint": "^8.21.0", "eslint-plugin-vue": "^9.3.0", "jsmpeg-player": "^3.0.3", "prettier": "^2.7.1", "sass": "^1.54.8", "vite": "^3.1.6", "vite-plugin-electron": "^0.10.4", "wait-on": "^6.0.1"}, "build": {"appId": "cloud.xueda", "productName": "惠达万安岗亭值守系统", "copyright": "Copyright © 2022 <XueDa>", "mac": {"category": "public.app-category.utilities"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "include": "electron/installer.nsh", "installerIcon": "public/favicon.ico"}, "files": ["dist/**/*", "electron/**/*"], "directories": {"buildResources": "assets", "output": "dist_electron"}, "electronDownload": {"mirror": "https://npmmirror.com/mirrors/electron/"}, "publish": {"provider": "generic", "url": "https://park.huidawanan.com/sentry-app/", "channel": "latest"}}}